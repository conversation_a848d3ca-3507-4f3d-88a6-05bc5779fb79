<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:if="${nav.source == 'indexNav'}" th:include="include :: header('修改首页导航菜单')" />
	<th:block th:if="${nav.source == 'infoNav'}" th:include="include :: header('修改信息导航菜单')" />
	<th:block th:if="${nav.source != 'indexNav' && nav.source != 'infoNav'}" th:include="include :: header('修改小程序菜单')" />
	<th:block th:include="include :: summernote-css" />
	<style>
		.attachment-list {
			border: 1px solid #ddd;
			border-radius: 4px;
			min-height: 60px;
			padding: 10px;
			background-color: #f9f9f9;
		}
		.attachment-item {
			display: inline-block;
			margin: 5px;
			padding: 8px 12px;
			background-color: #fff;
			border: 1px solid #ccc;
			border-radius: 4px;
			position: relative;
		}
		.attachment-item .file-icon {
			margin-right: 5px;
		}
		.attachment-item .remove-btn {
			margin-left: 8px;
			color: #d9534f;
			cursor: pointer;
		}
		.attachment-item .remove-btn:hover {
			color: #c9302c;
		}
		.attachment-empty {
			color: #999;
			font-style: italic;
			text-align: center;
			padding: 20px;
		}
	</style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-nav-edit">
            <input id="nav_id" name="nav_id" th:value="${nav.nav_id}" type="hidden">
            <input type="hidden" name="source" th:value="${nav.source}" />
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">菜单名称：</label>
                <div class="col-sm-10">
                    <input id="nav_name" name="nav_name" th:value="${nav.nav_name}" maxlength="8" minlength="2"
                           class="form-control" type="text" required pattern=".{2,8}" title="菜单名称必须是2-8个字符" placeholder="请输入2-8个字符">
                    <span class="help-block">菜单名称必须是2-8个字符</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">父菜单：</label>
                <div class="col-sm-10">
                    <div class="input-group">
                        <input id="parent_name" class="form-control" type="text" readonly placeholder="选择父菜单（不选择则为顶级菜单）">
                        <input id="parent_id" name="parent_id" type="hidden" th:value="${nav.parent_id}">
                        <div class="input-group-btn" style="float: none;">
                            <button class="btn btn-white" type="button" onclick="selectParentMenu()">
                                <i class="fa fa-search"></i> 选择
                            </button>
                            <button class="btn btn-white" type="button" onclick="clearParentMenu()">
                                <i class="fa fa-remove"></i> 清空
                            </button>
                        </div>
                    </div>
                    <span class="help-block">选择父菜单可创建二级菜单，不选择则创建顶级菜单</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">菜单描述：</label>
                <div class="col-sm-10">
                    <input id="remark" name="remark" th:value="${nav.remark}" maxlength="20" class="form-control" type="text"
                           placeholder="请输入菜单描述，最多20个字符">
                    <span class="help-block">菜单描述最多20个字符，用于主卡片显示</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">显示内容：</label>
                <div class="col-sm-10">
                    <div class="radio-box">
                        <input type="radio" name="nav_type" id="b1" value="text" checked>
                        <label for="b1">文本</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" name="nav_type" id="b2" value="pdf">
                        <label for="b2">PDF文件</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" name="nav_type" id="b3" value="url">
                        <label for="b3">URL链接</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" name="nav_type" id="b4" value="miniprogram">
                        <label for="b4">小程序跳转</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" name="nav_type" id="b5" value="page">
                        <label for="b5">内置页面</label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">图标选择：</label>
                <div class="col-sm-10">
                    <div class="input-group">
                        <input id="icon_name" name="icon_name" th:value="${nav.icon_name}" class="form-control" type="text" placeholder="请选择图标" readonly>
                        <span class="input-group-addon" id="icon_preview" style="min-width: 40px; text-align: center;">
                            <i class="fa fa-picture-o" style="color: #ccc;"></i>
                        </span>
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-primary" onclick="openIconSelector()">
                                <i class="fa fa-th"></i> 选择图标
                            </button>
                        </span>
                    </div>
                    <span class="help-block">点击选择图标按钮选择适合的Vant图标</span>
                </div>
            </div>
            <div class="form-group" id="pdf-section">
                <label class="col-sm-2 control-label">PDF文件：</label>
                <div class="col-sm-10">
                    <div class="attachment-container">
                        <div class="attachment-buttons">
                            <button type="button" class="btn btn-success btn-sm" onclick="openUploadPdfFiles()">
                                <i class="fa fa-paperclip"></i> 上传PDF文件
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="selectPdfFiles()">
                                <i class="fa fa-paperclip"></i> 选择PDF文件
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="selectFromDocumentLibrary()">
                                <i class="fa fa-book"></i> 选择公共文档库
                            </button>
                            <a href="https://www.pdfpai.com/word-to-pdf" style="color: #1a7bb9;" target="_blank">Word转PDF</a>
                        </div>
                        <div id="pdfFileList" class="attachment-list" style="margin-top: 10px;">
                            <!-- PDF文件列表将在这里显示 -->
                        </div>
                    </div>
                    <input type="hidden" id="pdfFileIds" name="pdfFileIds" />
                    <input type="hidden" id="pdf_file" name="pdf_file" th:value="${nav.pdf_file}" />
                    <input type="hidden" id="pdf_file_id" name="pdf_file_id" th:value="${nav.pdf_file_id}" />
                </div>
            </div>
            <div class="form-group" id="url-section">
                <label class="col-sm-2 control-label">微信文章链接：</label>
                <div class="col-sm-10">
                    <input id="url" name="url" class="form-control" type="text" th:value="${nav.url}" placeholder="请输入微信公众号文章链接">
                    <span class="help-block">
                        只支持微信公众号文章链接，必须以 https://mp.weixin.qq.com/ 开头<br>
                        示例：https://mp.weixin.qq.com/s/xxxxxxxxx
                    </span>
                </div>
            </div>
            <div class="form-group" id="miniprogram-section" style="display: none;">
                <label class="col-sm-2 control-label">小程序配置：</label>
                <div class="col-sm-10">
                    <div class="form-group">
                        <label class="control-label">小程序ID (appId)：</label>
                        <input id="miniprogram_appId" class="form-control" type="text" placeholder="如：wxd2ade0f25a874ee2">
                        <span class="help-block">必填，小程序的appId，以wx开头的18位字符</span>
                    </div>
                    <div class="form-group">
                        <label class="control-label">页面路径 (path)：</label>
                        <input id="miniprogram_path" class="form-control" type="text" placeholder="如：pages/index/index">
                        <span class="help-block">可选，要打开的页面路径，为空则打开首页</span>
                    </div>
                    <input type="hidden" id="miniprogram_config" name="miniprogram_config" th:value="${nav.miniprogram_config}" />
                </div>
            </div>
            <div class="form-group" id="page-section" style="display: none;">
                <label class="col-sm-2 control-label">内置页面：</label>
                <div class="col-sm-10">
                    <select id="tap_name" name="tap_name" class="form-control">
                        <option value="">请选择内置页面</option>
                        <option value="goToOcInfo">小区信息</option>
                        <option value="goToRepair">物业报修</option>
                        <option value="goToPayment">物业缴费</option>
                        <option value="goToChargeBill">缴费情况公开</option>
                        <option value="goToVisitor">邀请住户</option>
                        <option value="goToComplaint">投诉建议</option>
                        <option value="goToPropertyPhone">物业电话</option>
                        <option value="goServiceTel">服务电话列表</option>
                    </select>
                    <span class="help-block">选择要跳转的内置页面，这些页面已在小程序中预定义</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">菜单状态：</label>
                <div class="col-sm-10">
                    <div class="radio-box">
                        <input type="radio" name="status" id="a1" value="0" checked>
                        <label for="a1">启用</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" name="status" id="a2" value="1">
                        <label for="a2">停用</label>
                    </div>
                </div>
            </div>

            <div class="form-group" id="content-section">
                <label class="col-sm-2 control-label">菜单内容：</label>
                <div class="col-sm-10">
                    <input id="content" name="content" th:value="${nav.content}" type="hidden">
                    <div id="editor" class="summernote"></div>
                </div>
            </div>
		</form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: summernote-js" />
    <script type="text/javascript">
        var prefix = ctx + "oc/wxNav";
        var selectedPdfFiles = []; // 存储选中的PDF文件
    
	    $(function() {
            $('#form-nav-edit').renderForm({url:prefix+'/record'}, function(res) {
                $('.summernote').summernote({
                    placeholder: '请输入菜单内容，支持上传图片和截图粘贴',
                    height : 400,
                    lang : 'zh-CN',
                    followingToolbar: false,
                    dialogsInBody: true,
                    callbacks: {
                        onImageUpload: function (files) {
                            sendFile(files[0], this);
                        }
                    }
                });
                var content = $("#content").val();
                $('#editor').summernote('code', content);

                // 初始化nav_type选中状态
                initNavTypeSelection();

                // 初始化显示状态
                toggleContentSections();

                // 根据source限制nav_type选项
                initNavTypeRestrictions();

                // 初始化图标预览
                var currentIcon = $('#icon_name').val();
                if (currentIcon) {
                    updateIconPreview(currentIcon);
                }

                // 初始化PDF文件列表
                loadExistingPdfFiles();

                // 初始化小程序配置
                initMiniprogramConfig();

                // 初始化tap_name选择
                initTapNameConfig();

                // 初始化父菜单名称显示
                initParentMenuName();

                // 如果菜单有子菜单，隐藏内容相关字段
                initParentMenuContentVisibility();
            });

            // 监听 nav_type 的 iCheck change 事件
            $('input[name="nav_type"]').on('ifChanged', function() {
                toggleContentSections();
            });
        });

        // 根据source限制nav_type选项
        function initNavTypeRestrictions() {
            var source = $('input[name="source"]').val();
            if (source === 'infoNav') {
                // infoNav只支持text和pdf类型
                $('#b3').closest('.radio-box').hide(); // 隐藏URL选项
                $('#b4').closest('.radio-box').hide(); // 隐藏小程序选项
                $('#b5').closest('.radio-box').hide(); // 隐藏内置页面选项

                // 隐藏图标选择功能
                $('.form-group').has('#icon_name').hide();

                // 如果当前选中的是不支持的类型，切换到text
                var currentType = $('input[name="nav_type"]:checked').val();
                if (currentType === 'url' || currentType === 'miniprogram' || currentType === 'page') {
                    $('#b1').prop('checked', true).trigger('ifChecked');
                }
            }
        }

        // 根据 nav_type 的值切换显示内容
        function toggleContentSections() {
            var navType = $('input[name="nav_type"]:checked').val();

            // 隐藏所有区域
            $('#pdf-section').hide();
            $('#url-section').hide();
            $('#content-section').hide();
            $('#miniprogram-section').hide();
            $('#page-section').hide();

            if (navType === 'pdf') {
                // 显示 PDF 文件区域
                $('#pdf-section').show();
            } else if (navType === 'url') {
                // 显示 URL 链接区域
                $('#url-section').show();
            } else if (navType === 'miniprogram') {
                // 显示小程序配置区域
                $('#miniprogram-section').show();
            } else if (navType === 'page') {
                // 显示内置页面选择区域
                $('#page-section').show();
            } else {
                // 显示菜单内容区域（默认为text类型）
                $('#content-section').show();
            }
        }

        // 打开图标选择器
        function openIconSelector() {
           var index =   layer.open({
                type: 2,
                title: '选择图标',
                shadeClose: true,
                shade: 0.1,
                area: ['80%', '70%'],
                content: ctx + 'oc/wxNav/vant-icons'
            });
           layer.full(index);
        }

        // 图标选择回调函数
        function setSelectedIcon(iconName) {
            $('#icon_name').val(iconName);

            // 更新图标预览
            updateIconPreview(iconName);

            // 关闭弹窗
            layer.closeAll();

            $.modal.msgSuccess('图标选择成功：' + iconName);
        }

        // 更新图标预览
        function updateIconPreview(iconName) {
            var previewHtml = '';
            if (iconName) {
                // 这里使用简单的文本显示，因为后台页面无法直接渲染Vant图标
                previewHtml = '<span style="color: #07c160; font-weight: bold;">' + iconName + '</span>';
            } else {
                previewHtml = '<i class="fa fa-picture-o" style="color: #ccc;"></i>';
            }
            $('#icon_preview').html(previewHtml);
        }


        // 打开上传文件 - 使用新的工具类
        function openUploadPdfFiles(){
            var source = $('input[name="source"]').val();
            var businessId = $('#nav_id').val();

            FileAccessUtils.uploadFiles({
                fileType: 'pdf',
                source: source,
                bucketType: 'private',
                businessId: businessId
            }, function(uploadedFiles) {
                handleUploadedPdfFiles(uploadedFiles);
            });
        }

        // 使用工具类中的标准化函数
        function normalizeFileObject(fileData) {
            return FileAccessUtils.normalizeFileObject(fileData);
        }

        // 使用工具类中的文件存在检查函数
        function isFileExists(newFile, existingFiles) {
            return FileAccessUtils.isFileExists(newFile, existingFiles);
        }

        // 处理上传的PDF文件
        function handleUploadedPdfFiles(uploadedFiles) {
            try {
                if (!uploadedFiles || uploadedFiles.length === 0) {
                    $.modal.msgWarning('没有上传任何文件');
                    return;
                }

                var addedCount = 0;
                uploadedFiles.forEach(function(file) {
                    var normalizedFile = normalizeFileObject(file);
                    if (normalizedFile && !isFileExists(normalizedFile, selectedPdfFiles)) {
                        selectedPdfFiles.push(normalizedFile);
                        addedCount++;
                    }
                });

                // 更新显示和隐藏字段
                updatePdfFileDisplay();
                updatePdfFileIds();

                if (addedCount > 0) {
                    $.modal.msgSuccess('成功添加 ' + addedCount + ' 个PDF文件');
                } else {
                    $.modal.msgWarning('所有文件都已存在，未添加新文件');
                }

                console.log('PDF文件处理完成，当前文件列表:', selectedPdfFiles);
            } catch (e) {
                console.error('处理PDF文件时发生错误:', e);
                $.modal.msgError('处理PDF文件时发生错误：' + e.message);
            }
        }


        // 单选文件回调函数（用于indexNav）
        function setSelectedFile(fileUrl, fileName, fileId, fileData) {
            var source = $('input[name="source"]').val();

            if (source === 'indexNav') {
                // indexNav单选模式：设置单个文件信息
                $('#pdf_file').val(fileName);
                $('#pdf_file_id').val(fileId);
                $('#pdfFileIds').val(fileId);

                // 使用标准化的文件对象结构
                var normalizedFile = normalizeFileObject(fileData);

                selectedPdfFiles = [normalizedFile];
                updatePdfFileDisplay();

                $.modal.msgSuccess('PDF文件选择成功：' + fileName);
            }
        }


        // 加载现有PDF文件
        function loadExistingPdfFiles() {
            // 从后端获取现有的PDF文件列表
            var navId = $('#nav_id').val();
            if (navId) {
                $.ajax({
                    url: prefix + '/getPdfFileList',
                    type: 'POST',
                    data: { nav_id: navId },
                    dataType: 'json',
                    success: function(result) {
                        if (result.code === 0 && result.data) {
                            selectedPdfFiles = result.data;
                            updatePdfFileDisplay();
                            updatePdfFileIds();
                        }
                    },
                    error: function() {
                        console.log('加载PDF文件列表失败');
                    }
                });
            }
        }

        // 选择PDF文件 - 使用新的工具类
        function selectPdfFiles() {
            var source = $('input[name="source"]').val();

            if (source === 'indexNav') {
                // 单选模式
                FileAccessUtils.selectSingleFile({
                    fileType: 'pdf',
                    title: '选择PDF文件（单选）'
                }, function(file) {
                    $('#pdf_file').val(file.fileName);
                    $('#pdf_file_id').val(file.fileId);
                    $('#pdfFileIds').val(file.fileId);

                    selectedPdfFiles = [file];
                    updatePdfFileDisplay();

                    $.modal.msgSuccess('PDF文件选择成功：' + file.fileName);
                });
            } else {
                // 多选模式 - 叠加逻辑
                FileAccessUtils.selectMultipleFiles({
                    fileType: 'pdf',
                    title: '选择PDF文件（多选）'
                }, function(files) {
                    // 合并文件，避免重复
                    var addedCount = 0;
                    files.forEach(function(file) {
                        if (!FileAccessUtils.isFileExists(file, selectedPdfFiles)) {
                            selectedPdfFiles.push(file);
                            addedCount++;
                        }
                    });

                    updatePdfFileDisplay();
                    updatePdfFileIds();

                    if (addedCount > 0) {
                        $.modal.msgSuccess('成功添加 ' + addedCount + ' 个PDF文件');
                    } else {
                        $.modal.msgWarning('所有文件都已存在，未添加新文件');
                    }
                });
            }
        }

        // 多选文件回调函数（用于infoNav）
        function setSelectedAttachments(attachments) {
            var source = $('input[name="source"]').val();

            if (source === 'infoNav') {
                // infoNav多选模式：叠加逻辑，避免重复
                var addedCount = 0;
                if (attachments && attachments.length > 0) {
                    attachments.forEach(function(attachment) {
                        var normalizedFile = normalizeFileObject(attachment);
                        if (normalizedFile && !FileAccessUtils.isFileExists(normalizedFile, selectedPdfFiles)) {
                            selectedPdfFiles.push(normalizedFile);
                            addedCount++;
                        }
                    });
                }

                updatePdfFileDisplay();
                updatePdfFileIds();

                if (addedCount > 0) {
                    $.modal.msgSuccess('成功添加 ' + addedCount + ' 个PDF文件');
                } else {
                    $.modal.msgWarning('所有文件都已存在，未添加新文件');
                }
            }
        }

        // 更新PDF文件显示 - 使用新的工具类
        function updatePdfFileDisplay() {
            console.log('更新PDF文件显示，当前选中的文件:', selectedPdfFiles);
            try {
                var html = FileAccessUtils.createFileDisplayHtml(selectedPdfFiles, {
                    showRemove: true,
                    removeCallback: 'removePdfFile'
                });
                $('#pdfFileList').html(html);
            } catch (e) {
                console.error('更新PDF文件显示时发生错误:', e);
                $('#pdfFileList').html('<div class="attachment-empty">显示文件列表时出错</div>');
            }
        }

        // 删除PDF文件
        function removePdfFile(fileId, needDeleteServer) {
            selectedPdfFiles = FileAccessUtils.removeFile(selectedPdfFiles, fileId, needDeleteServer, function(updatedFiles, success, errorMsg) {
                selectedPdfFiles = updatedFiles;
                updatePdfFileDisplay();
                updatePdfFileIds();

                if (success) {
                    $.modal.msgSuccess('文件删除成功');
                } else {
                    $.modal.msgWarning('文件已从列表移除' + (errorMsg ? '，但服务器删除失败：' + errorMsg : ''));
                }
            });
        }

        // 更新PDF文件ID字段 - 使用新的工具类
        function updatePdfFileIds() {
            try {
                FileAccessUtils.updateFileIds(selectedPdfFiles, '#pdfFileIds');

                // 设置第一个文件为主文件
                if (selectedPdfFiles.length > 0) {
                    var firstFile = selectedPdfFiles[0];
                    $('#pdf_file').val(firstFile.fileName || '');
                    $('#pdf_file_id').val(firstFile.fileId || '');
                } else {
                    $('#pdf_file').val('');
                    $('#pdf_file_id').val('');
                }
            } catch (e) {
                console.error('更新PDF文件ID时发生错误:', e);
            }
        }



        // 初始化小程序配置
        function initMiniprogramConfig() {
            var configStr = $('#miniprogram_config').val();
            if (configStr&&configStr!='null') {
                try {
                    var config = JSON.parse(configStr);
                    $('#miniprogram_appId').val(config.appId || '');
                    $('#miniprogram_path').val(config.path || '');
                } catch (e) {
                    console.log('解析小程序配置失败:', e);
                }
            }
        }

        // 初始化nav_type选中状态
        function initNavTypeSelection() {
            // 从后端获取nav_type值并设置对应的单选按钮
            var navTypeValue = '[[${nav.nav_type}]]' || 'text';
            $('input[name="nav_type"][value="' + navTypeValue + '"]').prop('checked', true).trigger('ifChecked');
        }

        // 初始化tap_name配置
        function initTapNameConfig() {
            // 从后端获取tap_name值并设置选中状态
            var tapNameValue = '[[${nav.tap_name}]]' || '';
            if (tapNameValue) {
                $('#tap_name').val(tapNameValue);
            }
        }

	    // 上传文件 - 使用新的工具类
	    function sendFile(file, obj) {
	        FileAccessUtils.handleSummernoteUpload(file, obj, "wxNav", "public");
	    }

	    // 选择公共文档库文件
	    function selectFromDocumentLibrary() {
	        var source = $('input[name="source"]').val();

	        if (source === 'indexNav') {
	            // 单选模式
	            FileAccessUtils.selectDocumentLibrarySingleFile({
	                fileType: 'pdf',
	                title: '选择公共文档库PDF文件'
	            }, function(file) {
	                $('#pdf_file').val(file.fileName);
	                $('#pdf_file_id').val(file.fileId);
	                $('#pdfFileIds').val(file.fileId);

	                selectedPdfFiles = [file];
	                updatePdfFileDisplay();

	                $.modal.msgSuccess('公共文档库PDF文件选择成功：' + file.fileName);
	            });
	        } else {
	            // 多选模式
	            FileAccessUtils.selectDocumentLibraryFiles({
	                fileType: 'pdf',
	                title: '选择公共文档库PDF文件'
	            }, function(files) {
	                // 合并文件，避免重复
	                var addedCount = 0;
	                files.forEach(function(file) {
	                    if (!FileAccessUtils.isFileExists(file, selectedPdfFiles)) {
	                        selectedPdfFiles.push(file);
	                        addedCount++;
	                    }
	                });

	                updatePdfFileDisplay();
	                updatePdfFileIds();

	                if (addedCount > 0) {
	                    $.modal.msgSuccess('成功从公共文档库添加 ' + addedCount + ' 个PDF文件');
	                } else {
	                    $.modal.msgWarning('所有PDF文件都已存在，未添加新文件');
	                }
	            });
	        }
	    }
	    
		$("#form-nav-edit").validate({
			focusCleanup: true
		});
		
		function submitHandler() {
	        if ($.validate.form()) {
	        	// 验证菜单名称长度
	        	var navName = $('#nav_name').val();
	        	if (!navName || navName.length < 2 || navName.length > 8) {
	        		$.modal.alertWarning('菜单名称必须是2-8个字符');
	        		return;
	        	}

	        	var sHTML = $('.summernote').summernote('code');
				$("#content").val(sHTML);

				// 验证infoNav的nav_type限制
				var source = $('input[name="source"]').val();
				var navType = $('input[name="nav_type"]:checked').val();
				if (source === 'infoNav' && navType !== 'text' && navType !== 'pdf') {
					$.modal.alertWarning('信息导航菜单只支持文本和PDF类型');
					return;
				}

					// 验证page类型
					if (navType === 'page') {
						var tapName = $('#tap_name').val();
						if (!tapName) {
							$.modal.alertWarning('请选择内置页面');
							return;
						}
					}

				// 验证URL格式
				if (navType === 'url') {
					var url = $('#url').val();
                    if(url&&url.indexOf('ehome.getxf.cn')==-1){
                        if (url && !url.startsWith('https://mp.weixin.qq.com/')) {
                            $.modal.alertWarning('URL链接必须是微信公众号文章链接，以 https://mp.weixin.qq.com/ 开头');
                            return;
                        }
                    }
				}

				// 处理小程序配置
				if (navType === 'miniprogram') {
					var appId = $('#miniprogram_appId').val();
					if (!appId) {
						$.modal.alertWarning('请输入小程序ID');
						return;
					}
					if (!appId.startsWith('wx') || appId.length !== 18) {
						$.modal.alertWarning('小程序ID格式不正确，应该是以wx开头的18位字符');
						return;
					}
					var miniprogramConfig = {
						appId: appId,
						path: $('#miniprogram_path').val()
					};
					$('#miniprogram_config').val(JSON.stringify(miniprogramConfig));
				}

				var formData = $('#form-nav-edit').serialize();
				$.operate.save(prefix + "/edit", formData);
	        }
	    }

	    // 选择父菜单
	    function selectParentMenu() {
	        var source = $('input[name="source"]').val();
	        var url = prefix + "/tree?source=" + source;
	        var options = {
	            title: '选择父菜单',
	            width: "380",
	            url: url,
	            callBack: doSubmit
	        };
	        $.modal.openOptions(options);
	    }

	    // 清空父菜单
	    function clearParentMenu() {
	        $('#parent_id').val('0');
	        $('#parent_name').val('');
	    }

	    // 父菜单选择回调
	    function doSubmit(index, layero) {
	        var body = $.modal.getChildFrame(index);
	        $('#parent_id').val(body.find('#treeId').val());
	        $('#parent_name').val(body.find('#treeName').val());
	        $.modal.close(index);
	    }

	    // 初始化父菜单名称显示
	    function initParentMenuName() {
	        var parentId = $('#parent_id').val();
	        if (parentId && parentId !== '0') {
	            // 通过AJAX获取父菜单名称
	            $.get(prefix + "/record", {nav_id: parentId}, function(result) {
	                if (result.code === 0 && result.data) {
	                    $('#parent_name').val(result.data.nav_name);
	                }
	            });
	        }
	    }

	    // 初始化父菜单内容显示控制
	    function initParentMenuContentVisibility() {
	        var hasChildren = [[${nav.hasChildren}]];
	        if (hasChildren) {
	            // 隐藏显示内容选择区域（菜单类型选择）
	            $('label:contains("显示内容：")').closest('.form-group').hide();

	            // 隐藏内容相关的所有区域
	            $('#content-section').hide();
	            $('#pdf-section').hide();
	            $('#url-section').hide();
	            $('#miniprogram-section').hide();
	            $('#page-section').hide();

	            // 在表单最前面显示提示信息
	            $('#form-nav-edit').prepend('<div class="alert alert-info parent-menu-notice" style="margin-bottom: 20px;"><i class="fa fa-info-circle"></i> 此菜单为父菜单，不需要设置内容类型和具体内容。</div>');
	        }
	    }
	</script>

	<style>
	    .attachment-buttons {
	        margin-bottom: 10px;
	    }

	    .attachment-buttons .btn {
	        margin-right: 8px;
	    }

	    .attachment-buttons .btn:last-child {
	        margin-right: 0;
	    }
	</style>
</body>
</html>

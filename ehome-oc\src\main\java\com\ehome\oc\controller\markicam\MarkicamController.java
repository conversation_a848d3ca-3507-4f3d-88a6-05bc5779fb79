package com.ehome.oc.controller.markicam;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.jfinal.utils.Db;
import com.ehome.oc.service.MarkicamSyncService;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Markicam数据同步管理
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Controller
@RequestMapping("/oc/markicam")
public class MarkicamController extends BaseController {

    private static final String PREFIX = "oc/markicam";

    @Autowired
    private MarkicamSyncService markicamSyncService;

    /**
     * 主导航页面
     */
    @GetMapping("/index")
    public String index() {
        return PREFIX + "/index";
    }

    /**
     * 配置管理页面
     */
    @GetMapping("/config")
    public String config() {
        return PREFIX + "/config";
    }

    /**
     * 照片视频管理页面
     */
    @GetMapping("/moment")
    public String moment() {
        return PREFIX + "/moment";
    }

    /**
     * 团队成员管理页面
     */
    @GetMapping("/member")
    public String member() {
        return PREFIX + "/member";
    }

    /**
     * 团队管理页面
     */
    @GetMapping("/team")
    public String team() {
        return PREFIX + "/team";
    }


    /**
     * 违规车辆管理页面
     */
    @GetMapping("/illegal")
    public String illegal() {
        return PREFIX + "/illegal";
    }

    /**
     * 同步日志页面
     */
    @GetMapping("/log")
    public String log() {
        return PREFIX + "/log";
    }

    /**
     * 获取配置列表
     */
    @PostMapping("/config/list")
    @ResponseBody
    public TableDataInfo configList() {
        String communityId = getSysUser().getCommunityId();
        String sql = "SELECT * FROM eh_markicam_config WHERE community_id = ? AND is_deleted = 0";
        List<Record> list = Db.find(sql, communityId);
        return getDataList(list);
    }

    /**
     * 保存配置
     */
    @Log(title = "Markicam配置", businessType = BusinessType.INSERT)
    @PostMapping("/config/save")
    @ResponseBody
    public AjaxResult saveConfig() {
        JSONObject params = getParams();
        String communityId = getSysUser().getCommunityId();

        try {
            // 检查是否已存在配置
            Record existing = Db.findFirst(
                "SELECT id FROM eh_markicam_config WHERE community_id = ? AND org_id = ? AND is_deleted = 0",
                communityId, params.getString("org_id")
            );

            Record record = new Record();
            String currentTime = DateUtils.getTime();
            String loginName = getSysUser().getLoginName();

            if (existing != null) {
                // 更新
                record.set("id", existing.getLong("id"));
                record.set("api_key", params.getString("api_key"));
                record.set("base_url", params.getString("base_url"));
                record.set("is_enabled", params.getInteger("is_enabled"));
                record.set("sync_interval", params.getInteger("sync_interval"));
                record.set("updated_at", currentTime);
                record.set("updated_by", loginName);

                Db.update("eh_markicam_config", record);
            } else {
                // 新增
                record.set("community_id", communityId);
                record.set("org_id", params.getString("org_id"));
                record.set("api_key", params.getString("api_key"));
                record.set("base_url", params.getString("base_url"));
                record.set("is_enabled", params.getInteger("is_enabled"));
                record.set("sync_interval", params.getInteger("sync_interval"));
                record.set("created_at", currentTime);
                record.set("updated_at", currentTime);
                record.set("created_by", loginName);
                record.set("updated_by", loginName);
                record.set("is_deleted", 0);
                record.set("is_active", 1);

                Db.save("eh_markicam_config", record);
            }

            return AjaxResult.success("保存成功");

        } catch (Exception e) {
            logger.error("保存Markicam配置失败", e);
            return AjaxResult.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 手动同步照片视频数据
     */
    @Log(title = "同步照片视频", businessType = BusinessType.OTHER)
    @PostMapping("/sync/moment")
    @ResponseBody
    public AjaxResult syncMoment() {
        try {
            String communityId = getSysUser().getCommunityId();
            markicamSyncService.syncMomentData(communityId);
            return AjaxResult.success("同步完成");
        } catch (Exception e) {
            logger.error("同步照片视频数据失败", e);
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 按日期同步照片视频数据
     */
    @Log(title = "按日期同步照片视频", businessType = BusinessType.OTHER)
    @PostMapping("/sync/momentByDate")
    @ResponseBody
    public AjaxResult syncMomentByDate() {
        JSONObject params = getParams();
        try {
            String communityId = getSysUser().getCommunityId();
            String startTime = params.getString("start");
            String endTime = params.getString("end");
            Integer maxIterations = params.getInteger("max_iterations");

            if (startTime == null || endTime == null) {
                return AjaxResult.error("请提供开始和结束时间");
            }

            if (maxIterations == null || maxIterations <= 0) {
                maxIterations = 10;
            }

            int syncCount = markicamSyncService.syncMomentDataByDate(communityId, startTime, endTime, maxIterations);

            JSONObject result = new JSONObject();
            result.put("syncCount", syncCount);

            return AjaxResult.success("同步完成", result);
        } catch (Exception e) {
            logger.error("按日期同步照片视频数据失败", e);
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 手动同步成员数据
     */
    @Log(title = "同步成员数据", businessType = BusinessType.OTHER)
    @PostMapping("/sync/member")
    @ResponseBody
    public AjaxResult syncMember() {
        JSONObject params = getParams();
        try {
            String communityId = getSysUser().getCommunityId();
            Integer teamId = params.getInteger("team_id");
            markicamSyncService.syncMemberData(communityId, teamId);
            return AjaxResult.success("同步完成");
        } catch (Exception e) {
            logger.error("同步成员数据失败", e);
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 手动同步违规车辆数据
     */
    @Log(title = "同步违规车辆", businessType = BusinessType.OTHER)
    @PostMapping("/sync/illegal")
    @ResponseBody
    public AjaxResult syncIllegal() {
        try {
            String communityId = getSysUser().getCommunityId();
            markicamSyncService.syncIllegalParkData(communityId);
            return AjaxResult.success("同步完成");
        } catch (Exception e) {
            logger.error("同步违规车辆数据失败", e);
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 获取照片视频列表
     */
    @PostMapping("/moment/list")
    @ResponseBody
    public TableDataInfo momentList() {
        JSONObject params = getParams();
        String communityId = getSysUser().getCommunityId();

        EasySQL sql = new EasySQL("from eh_markicam_moment m " +
            "LEFT JOIN eh_markicam_team t ON m.team_id = t.team_id AND m.community_id = t.community_id " +
            "LEFT JOIN eh_markicam_member mem ON m.uid = mem.uid AND m.community_id = mem.community_id " +
            "WHERE m.community_id = '" + communityId + "' AND m.is_deleted = 0");

        // 添加查询条件
        sql.append(params.getInteger("team_id"), "AND m.team_id = ?");
        sql.append(params.getInteger("moment_type"), "AND m.moment_type = ?");
        sql.append(params.getString("start_time"), "AND m.post_time_str >= ?");
        sql.append(params.getString("end_time"), "AND m.post_time_str <= ?");
        sql.append(params.getInteger("is_public"), "AND m.is_public = ?");

        sql.append("ORDER BY m.post_time DESC");

        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "SELECT m.*, t.team_name, mem.nickname",
            sql.getSQL(),
            sql.getParams()
        );

        return getDataTable(paginate);
    }

    /**
     * 获取成员列表
     */
    @PostMapping("/member/list")
    @ResponseBody
    public TableDataInfo memberList() {
        JSONObject params = getParams();
        String communityId = getSysUser().getCommunityId();

        EasySQL sql = new EasySQL("from eh_markicam_member m " +
            "LEFT JOIN eh_markicam_team t ON m.team_id = t.team_id AND m.community_id = t.community_id " +
            "WHERE m.community_id = '" + communityId + "' AND m.is_deleted = 0");

        // 添加查询条件
        sql.append(params.getInteger("team_id"), "AND m.team_id = ?");
        sql.append(params.getInteger("member_type"), "AND m.member_type = ?");
        sql.appendLike(params.getString("nickname"), "AND m.nickname LIKE ?");
        sql.append(params.getString("phone"), "AND m.phone = ?");

        sql.append("ORDER BY m.join_time DESC");

        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "SELECT m.*, t.team_name",
            sql.getSQL(),
            sql.getParams()
        );

        return getDataTable(paginate);
    }

    /**
     * 获取违规车辆列表
     */
    @PostMapping("/illegal/list")
    @ResponseBody
    public TableDataInfo illegalList() {
        JSONObject params = getParams();
        String communityId = getSysUser().getCommunityId();

        EasySQL sql = new EasySQL("from eh_markicam_illegal_park i " +
            "LEFT JOIN eh_markicam_team t ON i.team_id = t.team_id AND i.community_id = t.community_id " +
            "WHERE i.community_id = '" + communityId + "' AND i.is_deleted = 0");

        // 添加查询条件
        sql.append(params.getInteger("team_id"), "AND i.team_id = ?");
        sql.append(params.getInteger("report_uid"), "AND i.report_uid = ?");
        sql.appendLike(params.getString("car_plate"), "AND i.car_plate LIKE ?");
        sql.append(params.getString("start_time"), "AND i.report_time_str >= ?");
        sql.append(params.getString("end_time"), "AND i.report_time_str <= ?");

        sql.append("ORDER BY i.report_time DESC");

        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "SELECT i.*, t.team_name",
            sql.getSQL(),
            sql.getParams()
        );

        return getDataTable(paginate);
    }

    /**
     * 获取同步日志
     */
    @PostMapping("/log/list")
    @ResponseBody
    public TableDataInfo logList() {
        JSONObject params = getParams();
        String communityId = getSysUser().getCommunityId();

        EasySQL sql = new EasySQL("from eh_markicam_sync_log " +
            "WHERE community_id = '" + communityId + "'");

        // 添加查询条件
        sql.append(params.getString("sync_type"), "AND sync_type = ?");
        sql.append(params.getInteger("sync_status"), "AND sync_status = ?");
        sql.append(params.getString("start_time"), "AND start_time >= ?");
        sql.append(params.getString("end_time"), "AND start_time <= ?");

        sql.append("ORDER BY created_at DESC");

        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "SELECT *",
            sql.getSQL(),
            sql.getParams()
        );

        return getDataTable(paginate);
    }

    /**
     * 获取团队列表
     */
    @PostMapping("/team/list")
    @ResponseBody
    public TableDataInfo teamList() {
        JSONObject params = getParams();
        String communityId = getSysUser().getCommunityId();

        EasySQL sql = new EasySQL("from eh_markicam_team " +
            "WHERE community_id = '" + communityId + "' AND is_deleted = 0");

        // 添加查询条件
        sql.appendLike(params.getString("team_name"), "AND team_name LIKE ?");
        sql.append(params.getInteger("team_id"), "AND team_id = ?");

        sql.append("ORDER BY team_name");

        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "SELECT *",
            sql.getSQL(),
            sql.getParams()
        );

        return getDataTable(paginate);
    }

    /**
     * 同步团队
     */
    @Log(title = "同步团队", businessType = BusinessType.OTHER)
    @PostMapping("/sync/team")
    @ResponseBody
    public AjaxResult syncTeam() {
        try {
            String communityId = getSysUser().getCommunityId();
            markicamSyncService.syncTeamData(communityId);
            return AjaxResult.success("同步完成");
        } catch (Exception e) {
            logger.error("同步团队失败", e);
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 获取团队详情
     */
    @GetMapping("/team/detail/{id}")
    @ResponseBody
    public AjaxResult getTeamDetail(@PathVariable("id") Long id) {
        try {
            Record team = Db.findFirst("SELECT * FROM eh_markicam_team WHERE id = ?", id);
            if (team == null) {
                return AjaxResult.error("团队不存在");
            }
            return AjaxResult.success(team.toMap());
        } catch (Exception e) {
            logger.error("获取团队详情失败", e);
            return AjaxResult.error("获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    @ResponseBody
    public AjaxResult getStatus() {
        try {
            String communityId = getSysUser().getCommunityId();
            JSONObject data = new JSONObject();

            // 获取配置数量
            Record configCountRecord = Db.findFirst(
                "SELECT COUNT(*) as count FROM eh_markicam_config WHERE community_id = ? AND is_enabled = 1 AND is_deleted = 0",
                communityId
            );
            data.put("configCount", configCountRecord != null ? configCountRecord.getLong("count") : 0);

            // 获取最后同步时间
            Record lastSync = Db.findFirst(
                "SELECT MAX(end_time) as last_sync_time FROM eh_markicam_sync_log WHERE community_id = ? AND sync_status = 1",
                communityId
            );
            if (lastSync != null) {
                data.put("lastSyncTime", lastSync.getStr("last_sync_time"));
            }

            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("获取系统状态失败", e);
            return AjaxResult.error("获取状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取数据统计
     */
    @GetMapping("/statistics")
    @ResponseBody
    public AjaxResult getStatistics() {
        try {
            String communityId = getSysUser().getCommunityId();
            JSONObject data = new JSONObject();

            // 统计各类数据数量
            Record momentCount = Db.findFirst("SELECT COUNT(*) as count FROM eh_markicam_moment WHERE community_id = ? AND is_deleted = 0", communityId);
            data.put("momentCount", momentCount != null ? momentCount.getLong("count") : 0);

            Record memberCount = Db.findFirst("SELECT COUNT(*) as count FROM eh_markicam_member WHERE community_id = ? AND is_deleted = 0", communityId);
            data.put("memberCount", memberCount != null ? memberCount.getLong("count") : 0);

            Record illegalCount = Db.findFirst("SELECT COUNT(*) as count FROM eh_markicam_illegal_park WHERE community_id = ? AND is_deleted = 0", communityId);
            data.put("illegalCount", illegalCount != null ? illegalCount.getLong("count") : 0);

            Record teamCount = Db.findFirst("SELECT COUNT(*) as count FROM eh_markicam_team WHERE community_id = ? AND is_deleted = 0", communityId);
            data.put("teamCount", teamCount != null ? teamCount.getLong("count") : 0);

            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("获取数据统计失败", e);
            return AjaxResult.error("获取统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取最近同步日志
     */
    @GetMapping("/log/recent")
    @ResponseBody
    public TableDataInfo getRecentLogs() {
        try {
            String communityId = getSysUser().getCommunityId();
            List<Record> logs = Db.find(
                "SELECT * FROM eh_markicam_sync_log WHERE community_id = ? ORDER BY created_at DESC LIMIT 10",
                communityId
            );
            return getDataList(logs);
        } catch (Exception e) {
            logger.error("获取最近日志失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 测试API连接
     */
    @PostMapping("/test/connection")
    @ResponseBody
    public AjaxResult testConnection() {
        JSONObject params = getParams();
        try {
            String communityId = getSysUser().getCommunityId();

            // 获取配置信息
            Record config = Db.findFirst(
                "SELECT * FROM eh_markicam_config WHERE community_id = ? AND is_enabled = 1 AND is_deleted = 0",
                communityId
            );

            if (config == null) {
                return AjaxResult.error("未找到有效的Markicam配置");
            }

            // 测试调用成员列表API（参数最少的接口）
            JSONObject testParams = new JSONObject();
            testParams.put("teamId", params.getInteger("team_id")); // 可选参数

            // 这里可以调用实际的API测试连接
            // JSONObject response = markicamSyncService.callMarkicamAPI("/marki/team/mem", testParams, config);

            return AjaxResult.success("连接测试成功，OkHttp3集成完成");
        } catch (Exception e) {
            logger.error("测试API连接失败", e);
            return AjaxResult.error("连接测试失败：" + e.getMessage());
        }
    }

    /**
     * 获取配置详情
     */
    @GetMapping("/config/detail/{id}")
    @ResponseBody
    public AjaxResult getConfigDetail(@PathVariable("id") Long id) {
        try {
            Record config = Db.findFirst("SELECT * FROM eh_markicam_config WHERE id = ?", id);
            if (config == null) {
                return AjaxResult.error("配置不存在");
            }
            return AjaxResult.success(config.toMap());
        } catch (Exception e) {
            logger.error("获取配置详情失败", e);
            return AjaxResult.error("获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取照片视频详情
     */
    @GetMapping("/moment/detail/{id}")
    @ResponseBody
    public AjaxResult getMomentDetail(@PathVariable("id") Long id) {
        try {
            Record moment = Db.findFirst(
                "SELECT m.*, t.team_name, mem.nickname FROM eh_markicam_moment m " +
                "LEFT JOIN eh_markicam_team t ON m.team_id = t.team_id AND m.community_id = t.community_id " +
                "LEFT JOIN eh_markicam_member mem ON m.uid = mem.uid AND m.community_id = mem.community_id " +
                "WHERE m.id = ?", id);
            if (moment == null) {
                return AjaxResult.error("记录不存在");
            }
            return AjaxResult.success(moment.toMap());
        } catch (Exception e) {
            logger.error("获取照片视频详情失败", e);
            return AjaxResult.error("获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 设置照片视频公示状态
     */
    @PostMapping("/moment/setPublic")
    @ResponseBody
    public AjaxResult setMomentPublic() {
        try {
            JSONObject params = getParams();
            Long id = params.getLong("id");
            Integer isPublic = params.getInteger("is_public");

            if (id == null || isPublic == null) {
                return AjaxResult.error("参数错误");
            }

            String communityId = getSysUser().getCommunityId();
            int result = Db.update(
                "UPDATE eh_markicam_moment SET is_public = ?, updated_at = ?, updated_by = ? WHERE id = ? AND community_id = ?",
                isPublic, DateUtils.getTime(), getSysUser().getLoginName(), id, communityId
            );

            if (result > 0) {
                return AjaxResult.success("设置成功");
            } else {
                return AjaxResult.error("设置失败，记录不存在");
            }
        } catch (Exception e) {
            logger.error("设置公示状态失败", e);
            return AjaxResult.error("设置失败：" + e.getMessage());
        }
    }

    /**
     * 批量设置照片视频公示状态
     */
    @PostMapping("/moment/batchSetPublic")
    @ResponseBody
    public AjaxResult batchSetMomentPublic() {
        try {
            JSONObject params = getParams();
            String ids = params.getString("ids");
            Integer isPublic = params.getInteger("is_public");

            if (StringUtils.isEmpty(ids) || isPublic == null) {
                return AjaxResult.error("参数错误");
            }

            String communityId = getSysUser().getCommunityId();
            String[] idArray = ids.split(",");
            int successCount = 0;

            for (String idStr : idArray) {
                try {
                    Long id = Long.parseLong(idStr.trim());
                    int result = Db.update(
                        "UPDATE eh_markicam_moment SET is_public = ?, updated_at = ?, updated_by = ? WHERE id = ? AND community_id = ?",
                        isPublic, DateUtils.getTime(), getSysUser().getLoginName(), id, communityId
                    );
                    if (result > 0) {
                        successCount++;
                    }
                } catch (NumberFormatException e) {
                    logger.warn("无效的ID: {}", idStr);
                }
            }

            return AjaxResult.success("批量设置完成，成功处理 " + successCount + " 条记录");
        } catch (Exception e) {
            logger.error("批量设置公示状态失败", e);
            return AjaxResult.error("批量设置失败：" + e.getMessage());
        }
    }

    /**
     * 获取成员详情
     */
    @GetMapping("/member/detail/{id}")
    @ResponseBody
    public AjaxResult getMemberDetail(@PathVariable("id") Long id) {
        try {
            Record member = Db.findFirst(
                "SELECT m.*, t.team_name FROM eh_markicam_member m " +
                "LEFT JOIN eh_markicam_team t ON m.team_id = t.team_id AND m.community_id = t.community_id " +
                "WHERE m.id = ?", id);
            if (member == null) {
                return AjaxResult.error("记录不存在");
            }
            return AjaxResult.success(member.toMap());
        } catch (Exception e) {
            logger.error("获取成员详情失败", e);
            return AjaxResult.error("获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取违规车辆详情
     */
    @GetMapping("/illegal/detail/{id}")
    @ResponseBody
    public AjaxResult getIllegalDetail(@PathVariable("id") Long id) {
        try {
            Record illegal = Db.findFirst(
                "SELECT i.*, t.team_name FROM eh_markicam_illegal_park i " +
                "LEFT JOIN eh_markicam_team t ON i.team_id = t.team_id AND i.community_id = t.community_id " +
                "WHERE i.id = ?", id);
            if (illegal == null) {
                return AjaxResult.error("记录不存在");
            }
            return AjaxResult.success(illegal.toMap());
        } catch (Exception e) {
            logger.error("获取违规车辆详情失败", e);
            return AjaxResult.error("获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取同步日志详情
     */
    @GetMapping("/log/detail/{id}")
    @ResponseBody
    public AjaxResult getLogDetail(@PathVariable("id") Long id) {
        try {
            Record log = Db.findFirst("SELECT * FROM eh_markicam_sync_log WHERE id = ?", id);
            if (log == null) {
                return AjaxResult.error("日志不存在");
            }
            return AjaxResult.success(log.toMap());
        } catch (Exception e) {
            logger.error("获取日志详情失败", e);
            return AjaxResult.error("获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 同步所有数据
     */
    @PostMapping("/sync/all")
    @ResponseBody
    public AjaxResult syncAllData() {
        try {
            String communityId = getSysUser().getCommunityId();

            // 依次同步各类数据
            markicamSyncService.syncTeamData(communityId);
            markicamSyncService.syncMemberData(communityId, null);
            markicamSyncService.syncMomentData(communityId);
            markicamSyncService.syncIllegalParkData(communityId);

            return AjaxResult.success("所有数据同步完成");
        } catch (Exception e) {
            logger.error("同步所有数据失败", e);
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }
}
